/*
Portfolio Enhancement CSS for <PERSON>ji
Linux Kernel Engineer <PERSON><PERSON><PERSON>
Based on Black & White Theme
*/

/**
 * 1.0 - Portfolio Color Variables & Theme
 */
:root {
  --kernel-dark: #1a365d;       /* Deep blue (Linux theme) */
  --rust-accent: #e14c38;       /* Rust orange */
  --cloud-light: #edf2f7;       /* Cloud/Infrastructure palette */
  --terminal-green: #00ff00;    /* Terminal green accent */
  --code-gray: #2d3748;         /* Code background */
  --text-primary: #333;         /* Primary text */
  --text-secondary: #666;       /* Secondary text */
  --border-light: #e2e8f0;      /* Light borders */
  --bg-white: #ffffff;          /* White background */
  --shadow-soft: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/**
 * 2.0 - Hero Section Styles
 */
.hero-section {
  background: linear-gradient(135deg, var(--bg-white) 0%, var(--cloud-light) 100%);
  padding: 80px 0;
  text-align: center;
  border-bottom: 2px solid var(--border-light);
  margin-bottom: 60px;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 300;
  color: var(--kernel-dark);
  margin-bottom: 20px;
  font-family: 'Lato', sans-serif;
}

.hero-subtitle {
  font-size: 1.8rem;
  color: var(--rust-accent);
  margin-bottom: 25px;
  font-weight: 400;
}

.hero-description {
  font-size: 1.2rem;
  color: var(--text-secondary);
  max-width: 800px;
  margin: 0 auto 40px;
  line-height: 1.6;
}

/**
 * 3.0 - Animated Skills Scroller
 */
.skills-scroller {
  background: var(--kernel-dark);
  color: white;
  padding: 15px 0;
  overflow: hidden;
  white-space: nowrap;
  margin: 40px 0;
  position: relative;
}

.skills-scroll-content {
  display: inline-block;
  animation: scroll-left 30s linear infinite;
  font-size: 1.1rem;
  font-weight: 300;
  letter-spacing: 2px;
}

@keyframes scroll-left {
  0% { transform: translateX(100%); }
  100% { transform: translateX(-100%); }
}

/**
 * 4.0 - Skills Matrix
 */
.skills-matrix {
  padding: 60px 0;
  background: var(--bg-white);
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
  margin-top: 40px;
}

.skill-category {
  background: var(--bg-white);
  padding: 30px;
  border-radius: 8px;
  box-shadow: var(--shadow-soft);
  border: 1px solid var(--border-light);
  text-align: center;
}

.skill-category h3 {
  color: var(--kernel-dark);
  margin-bottom: 25px;
  font-size: 1.4rem;
  font-weight: 400;
}

/**
 * 5.0 - Technology Badges
 */
.tech-badges {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 10px;
  margin-top: 20px;
}

.badge {
  background: var(--kernel-dark);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 300;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.badge:hover {
  background: var(--rust-accent);
  transform: translateY(-2px);
  box-shadow: var(--shadow-soft);
}

.badge.rust {
  background: var(--rust-accent);
}

.badge.linux {
  background: var(--kernel-dark);
}

.badge.cloud {
  background: var(--code-gray);
}

/**
 * 6.0 - Skill Progress Bars
 */
.skill-item {
  margin-bottom: 25px;
  text-align: left;
}

.skill-name {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-weight: 400;
  color: var(--text-primary);
}

.skill-bar {
  height: 8px;
  background: var(--border-light);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.skill-progress {
  height: 100%;
  border-radius: 4px;
  transition: width 2s ease-in-out;
  position: relative;
}

.skill-progress.c-lang { 
  background: var(--kernel-dark); 
  width: 90%; 
}

.skill-progress.rust-lang { 
  background: var(--rust-accent); 
  width: 65%; 
}

.skill-progress.python { 
  background: var(--code-gray); 
  width: 80%; 
}

.skill-progress.linux { 
  background: var(--kernel-dark); 
  width: 85%; 
}

.skill-progress.cloud { 
  background: var(--code-gray); 
  width: 75%; 
}

/**
 * 7.0 - Featured Projects Grid
 */
.featured-projects {
  padding: 60px 0;
  background: var(--cloud-light);
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  color: var(--kernel-dark);
  margin-bottom: 50px;
  font-weight: 300;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

.project-card {
  background: var(--bg-white);
  border-radius: 8px;
  padding: 30px;
  box-shadow: var(--shadow-soft);
  border: 1px solid var(--border-light);
  transition: all 0.3s ease;
  text-align: left;
}

.project-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.project-title {
  color: var(--kernel-dark);
  font-size: 1.4rem;
  margin-bottom: 15px;
  font-weight: 400;
}

.project-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 20px;
}

.project-links {
  display: flex;
  gap: 15px;
  margin-top: 20px;
}

.btn-project {
  padding: 10px 20px;
  border: 2px solid var(--kernel-dark);
  color: var(--kernel-dark);
  text-decoration: none;
  border-radius: 4px;
  font-weight: 400;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.btn-project:hover {
  background: var(--kernel-dark);
  color: white;
  text-decoration: none;
}

.btn-project.primary {
  background: var(--kernel-dark);
  color: white;
}

.btn-project.primary:hover {
  background: var(--rust-accent);
  border-color: var(--rust-accent);
}

/**
 * 8.0 - Professional Enhancements
 */
.cta-section {
  text-align: center;
  padding: 60px 0;
  background: var(--bg-white);
}

.cta-title {
  font-size: 2rem;
  color: var(--kernel-dark);
  margin-bottom: 20px;
  font-weight: 300;
}

.cta-description {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin-bottom: 30px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.btn-cta {
  display: inline-block;
  padding: 15px 30px;
  background: var(--rust-accent);
  color: white;
  text-decoration: none;
  border-radius: 4px;
  font-weight: 400;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  border: 2px solid var(--rust-accent);
}

.btn-cta:hover {
  background: var(--kernel-dark);
  border-color: var(--kernel-dark);
  color: white;
  text-decoration: none;
  transform: translateY(-2px);
  box-shadow: var(--shadow-soft);
}

/**
 * 9.0 - Responsive Design
 */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.4rem;
  }

  .hero-description {
    font-size: 1rem;
    padding: 0 20px;
  }

  .skills-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .projects-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .project-links {
    flex-direction: column;
    gap: 10px;
  }

  .btn-project {
    text-align: center;
  }

  .section-title {
    font-size: 2rem;
  }

  .skills-scroll-content {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 60px 0;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .skill-category {
    padding: 20px;
  }

  .project-card {
    padding: 20px;
  }

  .featured-projects,
  .skills-matrix,
  .cta-section {
    padding: 40px 0;
  }
}

/**
 * 10.0 - Animation Utilities
 */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.slide-in-left {
  opacity: 0;
  transform: translateX(-50px);
  transition: all 0.6s ease;
}

.slide-in-left.visible {
  opacity: 1;
  transform: translateX(0);
}

.slide-in-right {
  opacity: 0;
  transform: translateX(50px);
  transition: all 0.6s ease;
}

.slide-in-right.visible {
  opacity: 1;
  transform: translateX(0);
}
