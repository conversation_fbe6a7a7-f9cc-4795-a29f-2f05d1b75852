/*
Portfolio JavaScript for Daniel Orji
Linux Kernel Engineer Portfolio
*/

$(document).ready(function() {
    
    // Animate skill bars on scroll
    function animateSkillBars() {
        $('.skill-progress').each(function() {
            var $this = $(this);
            var skillTop = $this.offset().top;
            var skillBottom = skillTop + $this.outerHeight();
            var windowTop = $(window).scrollTop();
            var windowBottom = windowTop + $(window).height();
            
            if (skillBottom >= windowTop && skillTop <= windowBottom) {
                $this.addClass('animate');
            }
        });
    }
    
    // Fade in animations on scroll
    function fadeInOnScroll() {
        $('.fade-in, .slide-in-left, .slide-in-right').each(function() {
            var $this = $(this);
            var elementTop = $this.offset().top;
            var elementBottom = elementTop + $this.outerHeight();
            var windowTop = $(window).scrollTop();
            var windowBottom = windowTop + $(window).height();
            
            if (elementBottom >= windowTop && elementTop <= windowBottom) {
                $this.addClass('visible');
            }
        });
    }
    
    // Smooth scrolling for anchor links
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if (target.length) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 80
            }, 1000);
        }
    });
    
    // Project card hover effects
    $('.project-card').hover(
        function() {
            $(this).find('.tech-badges .badge').addClass('hover-effect');
        },
        function() {
            $(this).find('.tech-badges .badge').removeClass('hover-effect');
        }
    );
    
    // Skills scroller pause on hover
    $('.skills-scroller').hover(
        function() {
            $(this).find('.skills-scroll-content').css('animation-play-state', 'paused');
        },
        function() {
            $(this).find('.skills-scroll-content').css('animation-play-state', 'running');
        }
    );
    
    // Initialize animations on page load
    animateSkillBars();
    fadeInOnScroll();
    
    // Trigger animations on scroll
    $(window).scroll(function() {
        animateSkillBars();
        fadeInOnScroll();
    });
    
    // Add loading animation to buttons
    $('.btn-cta, .btn-project').on('click', function() {
        var $btn = $(this);
        var originalText = $btn.text();
        
        if (!$btn.hasClass('loading')) {
            $btn.addClass('loading');
            $btn.text('Loading...');
            
            setTimeout(function() {
                $btn.removeClass('loading');
                $btn.text(originalText);
            }, 2000);
        }
    });
    
    // Typing effect for hero title (optional enhancement)
    function typeWriter(element, text, speed) {
        var i = 0;
        element.innerHTML = '';
        
        function type() {
            if (i < text.length) {
                element.innerHTML += text.charAt(i);
                i++;
                setTimeout(type, speed);
            }
        }
        type();
    }
    
    // Initialize typing effect for hero title
    var heroTitle = document.querySelector('.hero-title');
    if (heroTitle) {
        var titleText = heroTitle.textContent;
        setTimeout(function() {
            typeWriter(heroTitle, titleText, 100);
        }, 500);
    }
    
    // Add parallax effect to hero section
    $(window).scroll(function() {
        var scrolled = $(this).scrollTop();
        var parallax = $('.hero-section');
        var speed = 0.5;
        
        parallax.css('transform', 'translateY(' + (scrolled * speed) + 'px)');
    });
    
    // Contact form enhancement (if contact form exists)
    $('#contact-form').on('submit', function(e) {
        e.preventDefault();
        
        var $form = $(this);
        var $submitBtn = $form.find('button[type="submit"]');
        var originalText = $submitBtn.text();
        
        $submitBtn.text('Sending...').prop('disabled', true);
        
        // Simulate form submission
        setTimeout(function() {
            $submitBtn.text('Message Sent!').removeClass('btn-primary').addClass('btn-success');
            
            setTimeout(function() {
                $submitBtn.text(originalText).prop('disabled', false).removeClass('btn-success').addClass('btn-primary');
                $form[0].reset();
            }, 3000);
        }, 2000);
    });
    
    // Add professional loading overlay
    function showLoadingOverlay() {
        $('body').append('<div class="loading-overlay"><div class="loading-spinner"></div></div>');
    }
    
    function hideLoadingOverlay() {
        $('.loading-overlay').fadeOut(500, function() {
            $(this).remove();
        });
    }
    
    // Professional page transitions
    $('a:not([href^="#"]):not([target="_blank"])').on('click', function(e) {
        var href = $(this).attr('href');
        
        if (href && href !== '#' && !href.startsWith('mailto:') && !href.startsWith('tel:')) {
            e.preventDefault();
            showLoadingOverlay();
            
            setTimeout(function() {
                window.location.href = href;
            }, 500);
        }
    });
    
    // Hide loading overlay on page load
    $(window).on('load', function() {
        hideLoadingOverlay();
    });
    
    // Add CSS for loading overlay
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .loading-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(255, 255, 255, 0.9);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
            }
            
            .loading-spinner {
                width: 40px;
                height: 40px;
                border: 4px solid #e2e8f0;
                border-top: 4px solid #1a365d;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            .skill-progress.animate {
                transition: width 2s ease-in-out;
            }
            
            .badge.hover-effect {
                transform: scale(1.1);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            }
            
            .btn-cta.loading,
            .btn-project.loading {
                opacity: 0.7;
                cursor: not-allowed;
            }
        `)
        .appendTo('head');
});
