<!DOCTYPE html>
<html lang="en">
	<head>
		<title><PERSON> - <PERSON>piring Linux Kernel Engineer | Portfolio</title>

		<!-- meta -->
		<meta charset="UTF-8">
	    <meta name="viewport" content="width=device-width, initial-scale=1">
	    <meta name="description" content="<PERSON> - Aspiring Linux Kernel Engineer specializing in C/Rust development, Linux systems, and cybersecurity. Available for relocation to the United States.">
	    <meta name="keywords" content="Linux Kernel Engineer, C Programming, Rust Developer, Cybersecurity, Proxmox, AWS, DevOps">
	    <meta name="author" content="Daniel Orji">

	    <!-- Open Graph / Social Media -->
	    <meta property="og:type" content="website">
	    <meta property="og:title" content="Daniel Orji - Linux Kernel Engineer Portfolio">
	    <meta property="og:description" content="Aspiring Linux Kernel Engineer specializing in C/Rust development and cybersecurity">
	    <meta property="og:url" content="">
	    <meta property="og:image" content="">

	    <!-- css -->
		<link rel="stylesheet" href="css/bootstrap.min.css">
		<link rel="stylesheet" href="css/ionicons.min.css">
		<link rel="stylesheet" href="css/pace.css">
	    <link rel="stylesheet" href="css/custom.css">
	    <link rel="stylesheet" href="css/portfolio.css">

	    <!-- js -->
	    <script src="js/jquery-2.1.3.min.js"></script>
	    <script src="js/bootstrap.min.js"></script>
	    <script src="js/pace.min.js"></script>
	    <script src="js/modernizr.custom.js"></script>
	</head>

	<body>
		<div class="container">	
			<header id="site-header">
				<div class="row">
					<div class="col-md-4 col-sm-5 col-xs-8">
						<div class="logo">
							<h1><a href="index.html"><b>Daniel</b> Orji</a></h1>
						</div>
					</div><!-- col-md-4 -->
					<div class="col-md-8 col-sm-7 col-xs-4">
						<nav class="main-nav" role="navigation">
							<div class="navbar-header">
  								<button type="button" id="trigger-overlay" class="navbar-toggle">
    								<span class="ion-navicon"></span>
  								</button>
							</div>

							<div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
  								<ul class="nav navbar-nav navbar-right">
    								<li class="cl-effect-11"><a href="index.html" data-hover="Home">Home</a></li>
    								<li class="cl-effect-11"><a href="projects.html" data-hover="Projects">Projects</a></li>
    								<li class="cl-effect-11"><a href="about.html" data-hover="About">About</a></li>
    								<li class="cl-effect-11"><a href="contact.html" data-hover="Contact">Contact</a></li>
  								</ul>
							</div><!-- /.navbar-collapse -->
						</nav>
						<div id="header-search-box">
							<a id="search-menu" href="#"><span id="search-icon" class="ion-ios-search-strong"></span></a>
							<div id="search-form" class="search-form">
								<form role="search" method="get" id="searchform" action="#">
									<input type="search" placeholder="Search" required>
									<button type="submit"><span class="ion-ios-search-strong"></span></button>
								</form>				
							</div>
						</div>
					</div><!-- col-md-8 -->
				</div>
			</header>
		</div>

		<!-- Hero Section -->
	<section class="hero-section">
		<div class="container">
			<div class="row">
				<div class="col-md-12">
					<h1 class="hero-title">Daniel Orji</h1>
					<h2 class="hero-subtitle">Aspiring Linux Kernel Engineer</h2>
					<p class="hero-description">
						Developing high-quality C/Rust solutions for Linux kernel development, Ubuntu systems, and cybersecurity.
						Passionate about low-level programming, system security, and infrastructure automation.
					</p>

					<!-- Animated Skills Scroller -->
					<div class="skills-scroller">
						<div class="skills-scroll-content">
							Kernel Fundamentals • C Programming • Rust Development • Proxmox VE • AWS Cloud • Docker • Linux Security • Device Drivers • System Programming • Cybersecurity
						</div>
					</div>

					<div class="hero-actions">
						<a href="projects.html" class="btn-cta">View My Projects</a>
						<a href="contact.html" class="btn-project" style="margin-left: 20px;">Get In Touch</a>
					</div>
				</div>
			</div>
		</div>
	</section>

	<div class="content-body">
			<div class="container">
				<div class="row">
					<main class="col-md-12">
						<!-- Skills Matrix Section -->
						<section class="skills-matrix">
							<h2 class="section-title">Technical Expertise</h2>
							<div class="skills-grid">
								<div class="skill-category">
									<h3>Programming Languages</h3>
									<div class="skill-item">
										<div class="skill-name">
											<span>C</span>
											<span>90%</span>
										</div>
										<div class="skill-bar">
											<div class="skill-progress c-lang"></div>
										</div>
									</div>
									<div class="skill-item">
										<div class="skill-name">
											<span>Rust</span>
											<span>65%</span>
										</div>
										<div class="skill-bar">
											<div class="skill-progress rust-lang"></div>
										</div>
									</div>
									<div class="skill-item">
										<div class="skill-name">
											<span>Python</span>
											<span>80%</span>
										</div>
										<div class="skill-bar">
											<div class="skill-progress python"></div>
										</div>
									</div>
									<div class="tech-badges">
										<span class="badge linux">C</span>
										<span class="badge rust">Rust</span>
										<span class="badge cloud">Python</span>
										<span class="badge">Bash</span>
									</div>
								</div>

								<div class="skill-category">
									<h3>Systems & Infrastructure</h3>
									<div class="skill-item">
										<div class="skill-name">
											<span>Linux Systems</span>
											<span>85%</span>
										</div>
										<div class="skill-bar">
											<div class="skill-progress linux"></div>
										</div>
									</div>
									<div class="skill-item">
										<div class="skill-name">
											<span>Cloud & DevOps</span>
											<span>75%</span>
										</div>
										<div class="skill-bar">
											<div class="skill-progress cloud"></div>
										</div>
									</div>
									<div class="tech-badges">
										<span class="badge linux">Ubuntu</span>
										<span class="badge linux">Proxmox VE</span>
										<span class="badge cloud">AWS</span>
										<span class="badge cloud">Docker</span>
									</div>
								</div>

								<div class="skill-category">
									<h3>Specializations</h3>
									<div class="tech-badges">
										<span class="badge linux">Kernel Development</span>
										<span class="badge rust">Device Drivers</span>
										<span class="badge cloud">System Security</span>
										<span class="badge">Network Programming</span>
										<span class="badge linux">Memory Management</span>
										<span class="badge rust">Concurrency</span>
									</div>
									<p style="margin-top: 20px; color: var(--text-secondary); text-align: center;">
										Focus on low-level system programming, security hardening, and performance optimization
									</p>
								</div>
							</div>
						</section>

						<!-- Featured Projects Section -->
						<section class="featured-projects">
							<h2 class="section-title">Featured Projects</h2>
							<div class="projects-grid">
								<div class="project-card">
									<h3 class="project-title">Proxmox VE Infrastructure</h3>
									<p class="project-description">
										Local infrastructure management with DNS servers, VS Code remote development environment,
										and service isolation using Proxmox VE virtualization platform.
									</p>
									<div class="tech-badges">
										<span class="badge linux">Proxmox VE</span>
										<span class="badge linux">Linux</span>
										<span class="badge">Bash</span>
										<span class="badge cloud">Networking</span>
									</div>
									<div class="project-links">
										<a href="projects.html#proxmox" class="btn-project primary">View Details</a>
										<a href="#" class="btn-project">Live Demo</a>
									</div>
								</div>

								<div class="project-card">
									<h3 class="project-title">Linux Kernel Modules</h3>
									<p class="project-description">
										Custom kernel modules and device drivers development focusing on memory management,
										system calls, and hardware interaction using C programming.
									</p>
									<div class="tech-badges">
										<span class="badge linux">C</span>
										<span class="badge linux">Kernel</span>
										<span class="badge rust">Device Drivers</span>
										<span class="badge">Assembly</span>
									</div>
									<div class="project-links">
										<a href="projects.html#kernel" class="btn-project primary">View Details</a>
										<a href="#" class="btn-project">Source Code</a>
									</div>
								</div>

								<div class="project-card">
									<h3 class="project-title">Rust System Tools</h3>
									<p class="project-description">
										High-performance system utilities and network programming tools built with Rust,
										focusing on memory safety and concurrent programming patterns.
									</p>
									<div class="tech-badges">
										<span class="badge rust">Rust</span>
										<span class="badge rust">Concurrency</span>
										<span class="badge cloud">Networking</span>
										<span class="badge">Performance</span>
									</div>
									<div class="project-links">
										<a href="projects.html#rust-tools" class="btn-project primary">View Details</a>
										<a href="#" class="btn-project">Source Code</a>
									</div>
								</div>
							</div>
						</section>

						<!-- Call to Action Section -->
						<section class="cta-section">
							<h2 class="cta-title">Ready to Collaborate?</h2>
							<p class="cta-description">
								I'm actively seeking opportunities in Linux kernel development and system programming.
								Available for relocation to the United States for the right opportunity.
							</p>
							<a href="contact.html" class="btn-cta">Get In Touch</a>
						</section>
					</main>

				</div>
			</div>
		</div>

		<footer id="site-footer">
			<div class="container">
				<div class="row">
					<div class="col-md-12">
						<p class="copyright">&copy; 2024 Daniel Orji - Linux Kernel Engineer</p>
					</div>
				</div>
			</div>
		</footer>

		<!-- Mobile Menu -->
		<div class="overlay overlay-hugeinc">
			<button type="button" class="overlay-close"><span class="ion-ios-close-empty"></span></button>
			<nav>
				<ul>
					<li><a href="index.html">Home</a></li>
					<li><a href="projects.html">Projects</a></li>
					<li><a href="about.html">About</a></li>
					<li><a href="contact.html">Contact</a></li>
				</ul>
			</nav>
		</div>

		<script src="js/script.js"></script>
		<script src="js/portfolio.js"></script>

	</body>
</html>